/**
 * SEO configuration for the application
 */

export const siteConfig = {
  name: "Remix Starter",
  description: "Build amazing applications with Remix, Vercel, and Neon Database.",
  tagline: "Modern Full-Stack Platform",
  url: "https://remixstarter.com",
  ogImage: "/logo-light.png",
  author: "Remix Starter Team",
  keywords: "Remix, Starter, Template, Vercel, Neon, Database, TypeScript, Tailwind CSS",
  twitterHandle: "@your-twitter",
  // supportedLocales: supportedLanguages, // Removed
  // defaultLocale: defaultLanguage, // Removed
  social: {
    github: "https://github.com/yourcompany",
    twitter: "https://twitter.com/yourcompany",
    linkedin: "https://linkedin.com/company/yourcompany",
    email: "mailto:<EMAIL>",
  },
  contact: {
    email: "<EMAIL>",
    support: "<EMAIL>",
    phone: "(*************",
  },
};

/**
 * Page-specific SEO configurations
 */
export const pageConfigs = {
  home: {
    title: "Welcome to Remix Starter",
    description:
      "A modern starter template with Remix, Vercel, Neon Database, and beautiful UI components",
    keywords: "Remix, Starter, Template, Full-stack, TypeScript",
  },
  components: {
    title: "UI Components Demo",
    description: "Showcase of beautiful shadcn/ui components integrated with Remix",
    keywords: "shadcn/ui, Components, UI, Design System, Tailwind CSS",
  },
  database: {
    title: "Database Integration",
    description: "Test and showcase database integration with Neon PostgreSQL",
    keywords: "Database, PostgreSQL, Neon, Drizzle ORM, SQL",
  },
  // i18n: { // Removed i18n page config
  //   title: "Internationalization Demo",
  //   description: "Multi-language support demonstration with i18next",
  //   keywords: "i18n, Internationalization, Multi-language, Translation",
  // },
  privacy: {
    title: "Privacy Policy",
    description: "Privacy Policy for our application",
    keywords: "Privacy, Policy, Legal, GDPR, Data Protection",
    noIndex: true,
  },
  terms: {
    title: "Terms of Service",
    description: "Terms of Service for our application",
    keywords: "Terms, Service, Legal, Agreement",
    noIndex: true,
  },
  agreement: {
    title: "Website Agreement",
    description: "Website Agreement and usage terms",
    keywords: "Agreement, Website, Terms, Usage",
    noIndex: true,
  },
};

/**
 * Generate page-specific metadata (simplified, no localization)
 */
export function getPageConfig(pageKey: keyof typeof pageConfigs) {
  return pageConfigs[pageKey];
}

/**
 * Generate canonical URL for a page (simplified, no locale)
 */
export function getCanonicalUrl(pathname: string): string {
  const baseUrl = siteConfig.url.replace(/\/$/, ""); // Remove trailing slash
  return `${baseUrl}${pathname}`;
}

/**
 * Generate structured data for the website
 */
export function getWebsiteStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: siteConfig.name,
    description: siteConfig.description,
    url: siteConfig.url,
    potentialAction: {
      "@type": "SearchAction",
      target: `${siteConfig.url}/search?q={search_term_string}`,
      "query-input": "required name=search_term_string",
    },
  };
}

/**
 * Generate organization structured data
 */
export function getOrganizationStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: siteConfig.name,
    description: siteConfig.description,
    url: siteConfig.url,
    logo: `${siteConfig.url}${siteConfig.ogImage}`,
    sameAs: [
      // Add your social media URLs here
      // "https://twitter.com/your-handle",
      // "https://github.com/your-org",
    ],
  };
}
