import type { AppLoadContext } from "@remix-run/node";
import type { AIEnvironmentVariables, AIProvider } from "./ai-providers";
import { createAIModel, validateProviderModel } from "./ai-providers";

/**
 * Extract AI environment variables from process.env
 */
export function getAIEnvironmentVariables(context?: AppLoadContext): AIEnvironmentVariables {
  return {
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY,
    OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY,
    SILICONFLOW_API_KEY: process.env.SILICONFLOW_API_KEY,
    SILICONFLOW_BASE_URL: process.env.SILICONFLOW_BASE_URL,
    REPLICATE_API_TOKEN: process.env.REPLICATE_API_TOKEN,
  };
}

/**
 * Validate AI request parameters
 */
export interface AIRequestParams {
  prompt: string;
  provider: <PERSON><PERSON><PERSON>ider;
  model: string;
}

export function validateAIRequest(params: any): params is AIRequestParams {
  if (!params || typeof params !== "object") {
    return false;
  }

  const { prompt, provider, model } = params;

  // Check required fields
  if (!prompt || typeof prompt !== "string" || prompt.trim().length === 0) {
    return false;
  }

  if (!provider || typeof provider !== "string") {
    return false;
  }

  if (!model || typeof model !== "string") {
    return false;
  }

  // Validate provider and model combination
  return validateProviderModel(provider as AIProvider, model);
}

/**
 * Create AI model with error handling
 */
export function createAIModelSafely(provider: AIProvider, model: string, context?: AppLoadContext) {
  try {
    const env = getAIEnvironmentVariables(context);
    return createAIModel(provider, model, env);
  } catch (error) {
    console.error(`Failed to create AI model for ${provider}/${model}:`, error);
    throw error;
  }
}

/**
 * Standard error messages for AI operations
 */
export const AI_ERROR_MESSAGES = {
  INVALID_PARAMS: "Invalid parameters provided",
  INVALID_PROVIDER: "Invalid or unsupported AI provider",
  INVALID_MODEL: "Invalid or unsupported model for the specified provider",
  MISSING_API_KEY: "API key is missing for the specified provider",
  GENERATION_FAILED: "AI text generation failed",
  STREAM_FAILED: "AI text streaming failed",
  IMAGE_GENERATION_FAILED: "AI image generation failed",
  PROVIDER_ERROR: "Provider-specific error occurred",
  RATE_LIMIT_EXCEEDED: "Rate limit exceeded for the AI provider",
  INSUFFICIENT_CREDITS: "Insufficient credits to perform this operation",
} as const;

/**
 * Check if error is related to API key issues
 */
export function isAPIKeyError(error: any): boolean {
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || "";
  const errorCode = error.code?.toLowerCase() || "";

  return (
    errorMessage.includes("api key") ||
    errorMessage.includes("unauthorized") ||
    errorMessage.includes("authentication") ||
    errorCode === "invalid_api_key" ||
    errorCode === "unauthorized"
  );
}

/**
 * Check if error is related to rate limiting
 */
export function isRateLimitError(error: any): boolean {
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || "";
  const errorCode = error.code?.toLowerCase() || "";

  return (
    errorMessage.includes("rate limit") ||
    errorMessage.includes("too many requests") ||
    errorCode === "rate_limit_exceeded" ||
    errorCode === "too_many_requests"
  );
}

/**
 * Get user-friendly error message from AI provider error
 */
export function getAIErrorMessage(error: any): string {
  if (!error) {
    return AI_ERROR_MESSAGES.GENERATION_FAILED;
  }

  if (isAPIKeyError(error)) {
    return AI_ERROR_MESSAGES.MISSING_API_KEY;
  }

  if (isRateLimitError(error)) {
    return AI_ERROR_MESSAGES.RATE_LIMIT_EXCEEDED;
  }

  // Return the original error message if it's user-friendly
  if (error.message && typeof error.message === "string") {
    return error.message;
  }

  return AI_ERROR_MESSAGES.GENERATION_FAILED;
}

/**
 * Log AI operation for debugging and monitoring
 */
export function logAIOperation(
  operation: string,
  provider: AIProvider,
  model: string,
  success: boolean,
  duration?: number,
  error?: any
) {
  const logData = {
    operation,
    provider,
    model,
    success,
    duration,
    timestamp: new Date().toISOString(),
    ...(error && { error: error.message || error }),
  };

  if (success) {
    console.log("AI Operation Success:", logData);
  } else {
    console.error("AI Operation Failed:", logData);
  }
}

/**
 * Sanitize prompt for logging (remove sensitive information)
 */
export function sanitizePromptForLogging(prompt: string, maxLength = 100): string {
  if (!prompt) return "";

  // Remove potential sensitive patterns
  let sanitized = prompt
    .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, "[EMAIL]")
    .replace(/\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, "[CARD]")
    .replace(/\b\d{3}-\d{2}-\d{4}\b/g, "[SSN]");

  // Truncate if too long
  if (sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength) + "...";
  }

  return sanitized;
}
