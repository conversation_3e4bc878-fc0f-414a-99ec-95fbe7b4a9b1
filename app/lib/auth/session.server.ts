/**
 * Session management utilities
 */

import { createTokenPair } from "./jwt.server";

export interface SessionData {
  userId: string;
  sessionToken: string;
  expiresAt: Date;
}

/**
 * Create authentication cookie
 */
export async function createAuthCookie(sessionData: SessionData): Promise<string> {
  const { userId, sessionToken, expiresAt } = sessionData;

  // Create JWT token for the session
  const tokens = await createTokenPair(
    {
      id: userId,
      uuid: userId, // Use userId as uuid for now
      email: "", // Will be filled from database
      name: "", // Will be filled from database
    },
    sessionToken
  );

  // Create secure cookie
  const isSecure = process.env.NODE_ENV === "production";
  const cookieOptions = [
    `auth-token=${tokens.accessToken}`,
    "Path=/",
    `Max-Age=${Math.floor((expiresAt.getTime() - Date.now()) / 1000)}`,
    "HttpOnly",
    "SameSite=Lax",
    ...(isSecure ? ["Secure"] : []),
  ];

  return cookieOptions.join("; ");
}

/**
 * Parse session from cookie
 */
export function parseSessionFromCookie(cookieHeader: string | null): string | null {
  if (!cookieHeader) return null;

  const cookies = cookieHeader.split(";").reduce(
    (acc, cookie) => {
      const [key, value] = cookie.trim().split("=");
      acc[key] = value;
      return acc;
    },
    {} as Record<string, string>
  );

  return cookies["auth-token"] || null;
}

/**
 * Clear session cookie
 */
export function clearSessionCookie(): string {
  return "auth-token=; Path=/; Max-Age=0; HttpOnly; SameSite=Lax";
}
