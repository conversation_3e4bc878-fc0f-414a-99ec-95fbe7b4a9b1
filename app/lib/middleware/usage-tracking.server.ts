/**
 * Usage Tracking Middleware
 * Automatically tracks API usage for all requests
 */

import type { AppLoadContext } from "@remix-run/node";
import {
  checkRateLimit,
  type RateLimitConfig,
  trackApiUsage,
  type UsageTrackingData,
} from "~/services/analytics.server";

// Note: getUserUuid import removed - will be handled in the calling code

// Rate limit configurations for different endpoints
const RATE_LIMIT_CONFIGS: Record<string, RateLimitConfig> = {
  "/api/ai/generate-text": {
    endpoint: "/api/ai/generate-text",
    windowMinutes: 60,
    maxRequests: 100,
    maxTokens: 50000,
    maxCredits: 500,
  },
  "/api/ai/stream-text": {
    endpoint: "/api/ai/stream-text",
    windowMinutes: 60,
    maxRequests: 100,
    maxTokens: 50000,
    maxCredits: 500,
  },
  "/api/ai/generate-image": {
    endpoint: "/api/ai/generate-image",
    windowMinutes: 60,
    maxRequests: 20,
    maxCredits: 200,
  },
  "/api/ai/cloudflare": {
    endpoint: "/api/ai/cloudflare",
    windowMinutes: 60,
    maxRequests: 200,
    maxTokens: 100000,
    maxCredits: 1000,
  },
};

export interface UsageTrackingContext {
  startTime: number;
  userUuid?: string;
  endpoint: string;
  method: string;
  requestSize?: number;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Initialize usage tracking for a request
 */
export async function initializeUsageTracking(
  request: Request,
  context: AppLoadContext
): Promise<UsageTrackingContext> {
  const url = new URL(request.url);
  const endpoint = url.pathname;
  const method = request.method;

  // Get user UUID if authenticated
  let userUuid: string | undefined;
  try {
    // Note: User UUID should be passed from the calling code
    // This will be handled in the API routes where user is already authenticated
  } catch (error) {
    // User not authenticated - continue without user tracking
  }

  // Get request metadata
  const ipAddress = getClientIP(request);
  const userAgent = request.headers.get("user-agent") || undefined;

  // Calculate request size
  let requestSize: number | undefined;
  try {
    const contentLength = request.headers.get("content-length");
    if (contentLength) {
      requestSize = parseInt(contentLength, 10);
    }
  } catch (error) {
    // Ignore request size calculation errors
  }

  return {
    startTime: Date.now(),
    userUuid,
    endpoint,
    method,
    requestSize,
    ipAddress,
    userAgent,
  };
}

/**
 * Check rate limits before processing request
 */
export async function checkRequestRateLimit(
  trackingContext: UsageTrackingContext,
  context: AppLoadContext
): Promise<{
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  headers: Record<string, string>;
}> {
  if (!trackingContext.userUuid || !context.db) {
    // Allow request if no user or database
    return {
      allowed: true,
      remaining: 1000,
      resetTime: new Date(Date.now() + 60 * 60 * 1000),
      headers: {},
    };
  }

  const config = RATE_LIMIT_CONFIGS[trackingContext.endpoint];
  if (!config) {
    // No rate limit configured for this endpoint
    return {
      allowed: true,
      remaining: 1000,
      resetTime: new Date(Date.now() + 60 * 60 * 1000),
      headers: {},
    };
  }

  try {
    const rateLimitResult = await checkRateLimit(
      trackingContext.userUuid,
      trackingContext.endpoint,
      config,
      context.db
    );

    const headers = {
      "X-RateLimit-Limit": config.maxRequests.toString(),
      "X-RateLimit-Remaining": rateLimitResult.remaining.toString(),
      "X-RateLimit-Reset": Math.floor(rateLimitResult.resetTime.getTime() / 1000).toString(),
    };

    return {
      allowed: rateLimitResult.allowed,
      remaining: rateLimitResult.remaining,
      resetTime: rateLimitResult.resetTime,
      headers,
    };
  } catch (error) {
    console.error("Error checking rate limit:", error);
    // Allow request on error
    return {
      allowed: true,
      remaining: 1000,
      resetTime: new Date(Date.now() + 60 * 60 * 1000),
      headers: {},
    };
  }
}

/**
 * Track API usage after request completion
 */
export async function trackRequestUsage(
  trackingContext: UsageTrackingContext,
  response: Response,
  context: AppLoadContext,
  additionalData?: {
    provider?: string;
    model?: string;
    tokensUsed?: number;
    creditsUsed?: number;
    errorCode?: string;
    errorMessage?: string;
    metadata?: Record<string, any>;
  }
): Promise<void> {
  if (!trackingContext.userUuid || !context.db) {
    return; // Skip tracking if no user or database
  }

  try {
    const duration = Date.now() - trackingContext.startTime;

    // Calculate response size
    let responseSize: number | undefined;
    try {
      const contentLength = response.headers.get("content-length");
      if (contentLength) {
        responseSize = parseInt(contentLength, 10);
      }
    } catch (error) {
      // Ignore response size calculation errors
    }

    // Determine status
    let status: "success" | "error" | "timeout";
    if (response.status >= 200 && response.status < 300) {
      status = "success";
    } else if (response.status === 408 || response.status === 504) {
      status = "timeout";
    } else {
      status = "error";
    }

    const usageData: UsageTrackingData = {
      userUuid: trackingContext.userUuid,
      endpoint: trackingContext.endpoint,
      method: trackingContext.method,
      provider: additionalData?.provider,
      model: additionalData?.model,
      requestSize: trackingContext.requestSize,
      responseSize,
      tokensUsed: additionalData?.tokensUsed,
      creditsUsed: additionalData?.creditsUsed,
      duration,
      status,
      errorCode: additionalData?.errorCode,
      errorMessage: additionalData?.errorMessage,
      ipAddress: trackingContext.ipAddress,
      userAgent: trackingContext.userAgent,
      metadata: additionalData?.metadata,
    };

    await trackApiUsage(usageData, context.db);
  } catch (error) {
    console.error("Error tracking API usage:", error);
    // Don't throw error to avoid affecting the main request
  }
}

/**
 * Create rate limit response
 */
export function createRateLimitResponse(
  remaining: number,
  resetTime: Date,
  headers: Record<string, string>
): Response {
  return new Response(
    JSON.stringify({
      error: "Rate limit exceeded",
      message: "Too many requests. Please try again later.",
      remaining,
      resetTime: resetTime.toISOString(),
    }),
    {
      status: 429,
      headers: {
        "Content-Type": "application/json",
        ...headers,
      },
    }
  );
}

/**
 * Get client IP address from request
 */
function getClientIP(request: Request): string | undefined {
  // Check various headers for client IP
  const headers = [
    "cf-connecting-ip", // Cloudflare
    "x-forwarded-for",
    "x-real-ip",
    "x-client-ip",
  ];

  for (const header of headers) {
    const value = request.headers.get(header);
    if (value) {
      // Take the first IP if there are multiple
      return value.split(",")[0].trim();
    }
  }

  return undefined;
}

/**
 * Extract tokens used from AI response
 */
export function extractTokensFromResponse(response: any): number | undefined {
  if (!response || typeof response !== "object") {
    return undefined;
  }

  // Check various possible locations for token usage
  if (response.usage?.total_tokens) {
    return response.usage.total_tokens;
  }

  if (response.usage?.totalTokens) {
    return response.usage.totalTokens;
  }

  if (response.tokens) {
    return response.tokens;
  }

  return undefined;
}

/**
 * Extract model information from AI response
 */
export function extractModelFromResponse(response: any): {
  provider?: string;
  model?: string;
} {
  if (!response || typeof response !== "object") {
    return {};
  }

  return {
    provider: response.provider,
    model: response.model,
  };
}
