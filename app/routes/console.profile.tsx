/**
 * User Profile Management Page
 * Allows users to view and edit their personal information
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import {
  AlertCircle,
  Calendar,
  Camera,
  CheckCircle,
  CreditCard,
  Mail,
  Save,
  Upload,
  User,
} from "lucide-react";
import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Separator } from "~/components/ui/separator";
import { Textarea } from "~/components/ui/textarea";
import { createDbFromEnv } from "~/lib/db";
import { findUserByUuid, updateUser } from "~/models/user";
import { getUserUuid } from "~/services/user-management.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv();

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const user = await findUserByUuid(userUuid, db);
    if (!user) {
      return json({ success: false, error: "User not found" }, { status: 404 });
    }

    return json({
      success: true,
      data: {
        user: {
          id: user.id,
          uuid: user.uuid,
          name: user.name,
          email: user.email,
          avatar: user.avatar,
          credits: user.credits,
          inviteCode: user.invite_code,
          createdAt: user.created_at,
          updatedAt: user.updated_at,
        },
      },
    });
  } catch (error) {
    console.error("Error loading user profile:", error);
    return json({ success: false, error: "Failed to load profile" }, { status: 500 });
  }
}

export async function action({ request, context }: ActionFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv();

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const formData = await request.formData();
    const action = formData.get("action") as string;

    if (action === "update-profile") {
      const name = formData.get("name") as string;
      const bio = formData.get("bio") as string;

      // Validate input
      if (!name || name.trim().length < 2) {
        return json({
          success: false,
          error: "Name must be at least 2 characters long",
        });
      }

      // Update user profile
      await updateUser(userUuid, { name: name.trim() }, db);

      return json({
        success: true,
        message: "Profile updated successfully",
      });
    }

    if (action === "update-avatar") {
      const avatarUrl = formData.get("avatarUrl") as string;

      if (avatarUrl && !isValidUrl(avatarUrl)) {
        return json({
          success: false,
          error: "Please provide a valid image URL",
        });
      }

      await updateUser(userUuid, { avatar: avatarUrl || null }, db);

      return json({
        success: true,
        message: "Avatar updated successfully",
      });
    }

    return json({ success: false, error: "Invalid action" });
  } catch (error) {
    console.error("Error updating profile:", error);
    return json({ success: false, error: "Failed to update profile" }, { status: 500 });
  }
}

function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

export default function ProfilePage() {
  const { data } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const [avatarUrl, setAvatarUrl] = useState(data.user.avatar || "");

  const isSubmitting = navigation.state === "submitting";
  const isUpdatingProfile = navigation.formData?.get("action") === "update-profile";
  const isUpdatingAvatar = navigation.formData?.get("action") === "update-avatar";

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Profile Settings</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Manage your personal information and account preferences
          </p>
        </div>

        {/* Success/Error Messages */}
        {actionData && (
          <div
            className={`mb-6 p-4 rounded-lg flex items-center space-x-2 ${
              actionData.success
                ? "bg-green-50 text-green-800 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
                : "bg-red-50 text-red-800 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"
            }`}
          >
            {actionData.success ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <AlertCircle className="w-5 h-5" />
            )}
            <span>{actionData.message || actionData.error}</span>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Overview */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="w-5 h-5" />
                  <span>Profile Overview</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Avatar */}
                <div className="flex flex-col items-center space-y-4">
                  <Avatar className="w-24 h-24">
                    <AvatarImage src={data.user.avatar} alt={data.user.name} />
                    <AvatarFallback className="text-lg">
                      {getInitials(data.user.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-center">
                    <h3 className="text-lg font-semibold">{data.user.name}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{data.user.email}</p>
                  </div>
                </div>

                <Separator />

                {/* Account Stats */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Credits</span>
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <CreditCard className="w-3 h-3" />
                      <span>{data.user.credits}</span>
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Member Since</span>
                    <span className="text-sm font-medium">
                      {new Date(data.user.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  {data.user.inviteCode && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Invite Code</span>
                      <Badge variant="outline">{data.user.inviteCode}</Badge>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Profile Forms */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>
                  Update your personal details and contact information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form method="post" className="space-y-4">
                  <input type="hidden" name="action" value="update-profile" />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Full Name</Label>
                      <Input
                        id="name"
                        name="name"
                        defaultValue={data.user.name}
                        placeholder="Enter your full name"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={data.user.email}
                        disabled
                        className="bg-gray-50 dark:bg-gray-800"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Email cannot be changed. Contact support if needed.
                      </p>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="bio">Bio (Optional)</Label>
                    <Textarea
                      id="bio"
                      name="bio"
                      placeholder="Tell us a bit about yourself..."
                      rows={3}
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isUpdatingProfile}
                    className="flex items-center space-x-2"
                  >
                    <Save className="w-4 h-4" />
                    <span>{isUpdatingProfile ? "Saving..." : "Save Changes"}</span>
                  </Button>
                </Form>
              </CardContent>
            </Card>

            {/* Avatar Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Camera className="w-5 h-5" />
                  <span>Profile Picture</span>
                </CardTitle>
                <CardDescription>
                  Update your profile picture with a custom image URL
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form method="post" className="space-y-4">
                  <input type="hidden" name="action" value="update-avatar" />

                  <div>
                    <Label htmlFor="avatarUrl">Image URL</Label>
                    <Input
                      id="avatarUrl"
                      name="avatarUrl"
                      type="url"
                      value={avatarUrl}
                      onChange={(e) => setAvatarUrl(e.target.value)}
                      placeholder="https://example.com/your-image.jpg"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Provide a direct link to your profile image (JPG, PNG, GIF)
                    </p>
                  </div>

                  {avatarUrl && (
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Preview:</span>
                      <Avatar className="w-12 h-12">
                        <AvatarImage src={avatarUrl} alt="Preview" />
                        <AvatarFallback>{getInitials(data.user.name)}</AvatarFallback>
                      </Avatar>
                    </div>
                  )}

                  <Button
                    type="submit"
                    disabled={isUpdatingAvatar}
                    variant="outline"
                    className="flex items-center space-x-2"
                  >
                    <Upload className="w-4 h-4" />
                    <span>{isUpdatingAvatar ? "Updating..." : "Update Avatar"}</span>
                  </Button>
                </Form>
              </CardContent>
            </Card>

            {/* Account Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="w-5 h-5" />
                  <span>Account Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>User ID</Label>
                    <div className="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded border text-sm font-mono">
                      {data.user.uuid}
                    </div>
                  </div>
                  <div>
                    <Label>Account Created</Label>
                    <div className="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded border text-sm">
                      {new Date(data.user.createdAt).toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <Label>Last Updated</Label>
                    <div className="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded border text-sm">
                      {new Date(data.user.updatedAt).toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <Label>Current Credits</Label>
                    <div className="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded border text-sm font-semibold text-green-600">
                      {data.user.credits} credits
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
