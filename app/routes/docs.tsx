import type { MetaFunction } from "@remix-run/node";
import { BookO<PERSON>, Code, FileText, Zap } from "lucide-react";
import type { SidebarItem } from "~/components/layout/sidebar";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export const meta: MetaFunction = () => {
  return [
    { title: "Documentation - AI SaaS Platform" },
    { name: "description", content: "Complete documentation for our AI SaaS platform" },
  ];
};

// Custom sidebar configuration for documentation
const docsSidebarItems: SidebarItem[] = [
  {
    title: "Getting Started",
    url: "/docs",
    icon: <Zap className="h-5 w-5" />,
    description: "Quick start guide",
  },
  {
    title: "API Reference",
    url: "/docs/api",
    icon: <Code className="h-5 w-5" />,
    description: "Complete API documentation",
    children: [
      {
        title: "Authentication",
        url: "/docs/api/auth",
        icon: <FileText className="h-4 w-4" />,
        description: "API authentication",
      },
      {
        title: "AI Endpoints",
        url: "/docs/api/ai",
        icon: <FileText className="h-4 w-4" />,
        description: "AI API endpoints",
      },
      {
        title: "User Management",
        url: "/docs/api/users",
        icon: <FileText className="h-4 w-4" />,
        description: "User management API",
      },
    ],
  },
  {
    title: "Examples",
    url: "/docs/examples",
    icon: <FileText className="h-5 w-5" />,
    description: "Code examples and tutorials",
    children: [
      {
        title: "React Integration",
        url: "/docs/examples/react",
        icon: <Code className="h-4 w-4" />,
        description: "Using with React",
      },
      {
        title: "Node.js Examples",
        url: "/docs/examples/nodejs",
        icon: <Code className="h-4 w-4" />,
        description: "Server-side examples",
      },
    ],
  },
  {
    title: "Guides",
    url: "/docs/guides",
    icon: <BookOpen className="h-5 w-5" />,
    description: "Step-by-step guides",
  },
];

export default function DocsPage() {
  return (
    <UnifiedLayout
      showSidebar={true}
      headerProps={{}}
      sidebarProps={{
        title: "Documentation",
        items: docsSidebarItems,
      }}
    >
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Documentation</h1>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            Everything you need to know about our AI SaaS platform
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                  <Zap className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <CardTitle>Quick Start</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Get up and running with our platform in minutes. Follow our step-by-step guide to
                integrate AI into your applications.
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                  <Code className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <CardTitle>API Reference</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Complete API documentation with examples, parameters, and response formats for all
                endpoints.
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                  <FileText className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <CardTitle>Examples</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Real-world examples and code snippets to help you implement AI features in your
                applications.
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                </div>
                <CardTitle>Guides</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Comprehensive guides covering advanced topics, best practices, and optimization
                techniques.
              </CardDescription>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Getting Started</CardTitle>
            <CardDescription>Welcome to our AI SaaS platform documentation</CardDescription>
          </CardHeader>
          <CardContent className="prose dark:prose-invert max-w-none">
            <h3>Overview</h3>
            <p>
              Our AI SaaS platform provides powerful artificial intelligence capabilities through
              easy-to-use APIs. Whether you're building a chatbot, generating content, or analyzing
              data, our platform has you covered.
            </p>

            <h3>Key Features</h3>
            <ul>
              <li>
                <strong>Text Generation:</strong> Generate high-quality text content using advanced
                language models
              </li>
              <li>
                <strong>Chat Interface:</strong> Build conversational AI applications with our chat
                API
              </li>
              <li>
                <strong>Code Assistant:</strong> Get AI-powered help with coding tasks and debugging
              </li>
              <li>
                <strong>Image Generation:</strong> Create stunning images from text descriptions
              </li>
            </ul>

            <h3>Quick Start</h3>
            <p>To get started with our platform, you'll need to:</p>
            <ol>
              <li>Sign up for an account</li>
              <li>Get your API key from the dashboard</li>
              <li>Make your first API call</li>
              <li>Integrate with your application</li>
            </ol>

            <h3>Need Help?</h3>
            <p>
              If you have questions or need assistance, check out our community forum or contact our
              support team.
            </p>
          </CardContent>
        </Card>
      </div>
    </UnifiedLayout>
  );
}
