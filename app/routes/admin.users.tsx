import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { UserDataTable } from "~/components/admin/SearchableDataTable";
import { createDbFromEnv } from "~/lib/db";
import { searchUsers } from "~/models/user";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // Create database connection
    const db = context.db || createDbFromEnv();

    // Parse search parameters
    const url = new URL(request.url);
    const searchParams = {
      search: url.searchParams.get("search") || undefined,
      page: parseInt(url.searchParams.get("page") || "1"),
      limit: parseInt(url.searchParams.get("limit") || "20"),
      sortBy: url.searchParams.get("sortBy") || "createdAt",
      sortOrder: (url.searchParams.get("sortOrder") as "asc" | "desc") || "desc",
      isAffiliate:
        url.searchParams.get("isAffiliate") === "true"
          ? true
          : url.searchParams.get("isAffiliate") === "false"
            ? false
            : undefined,
      minCredits: url.searchParams.get("minCredits")
        ? parseInt(url.searchParams.get("minCredits")!)
        : undefined,
      maxCredits: url.searchParams.get("maxCredits")
        ? parseInt(url.searchParams.get("maxCredits")!)
        : undefined,
    };

    // Search users
    const result = await searchUsers(db, searchParams);

    return json({
      success: true,
      data: result.data,
      pagination: result.pagination,
      searchParams,
    });
  } catch (error) {
    console.error("Error loading users:", error);
    return json(
      {
        success: false,
        error: "Failed to load users",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

export default function AdminUsers() {
  const { data, pagination } = useLoaderData<typeof loader>();

  const handleUserAction = (action: string, user: any) => {
    console.log(`${action} user:`, user);
    // TODO: Implement user actions
  };

  if (!data) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Failed to load users</h1>
          <p className="text-gray-600">Please try refreshing the page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <UserDataTable
          data={data}
          pagination={pagination}
          onUserAction={handleUserAction}
          title="User Management"
          subtitle="Manage and search through all users in your application"
          headerActions={
            <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Add User
            </button>
          }
        />
      </div>
    </div>
  );
}
