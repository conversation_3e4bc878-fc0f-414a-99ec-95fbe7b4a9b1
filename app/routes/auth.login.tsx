/**
 * Sign In Page - Google One Tap Priority with Neon Auth Fallback
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { GoogleOneTap, NeonFallbackButton } from "~/components/auth/google-one-tap";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { getUser } from "~/lib/auth/middleware.server";

export const meta: MetaFunction = () => {
  return [
    { title: "Sign In - AI SaaS Starter" },
    { name: "description", content: "Sign in to your account" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  // 检查用户是否已经登录
  const userResult = await getUser(request);

  if (userResult.success) {
    // 用户已登录，重定向到控制台
    return redirect("/console");
  }

  // 返回 Google Client ID（从环境变量获取）
  return json({
    googleClientId: process.env.GOOGLE_CLIENT_ID,
  });
}

export default function SignInPage() {
  const { googleClientId } = useLoaderData<typeof loader>();

  return (
    <UnifiedLayout showHeader={false} showFooter={false} showSidebar={false} containerSize="full">
      {/* Background Effects */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl animate-pulse" />
        <div className="absolute top-40 right-20 w-32 h-32 bg-gradient-to-r from-purple-500/15 to-pink-500/15 rounded-full blur-2xl animate-pulse delay-1000" />
        <div className="absolute bottom-40 left-1/4 w-24 h-24 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-full blur-xl animate-pulse delay-2000" />
      </div>

      <section className="py-32 relative overflow-hidden min-h-screen flex items-center">
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-md mx-auto">
            {/* Header */}
            <div className="text-center mb-12 animate-fade-in-up">
              <h1 className="text-4xl font-extrabold text-gray-900 dark:text-white mb-4 tracking-tight">
                Sign In to Your Account
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 font-medium">
                Access your AI workspace with secure authentication
              </p>
            </div>

            {/* Auth Card */}
            <div className="group relative animate-fade-in-up delay-200">
              <div className="absolute -inset-1 bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-cyan-600/20 rounded-3xl blur opacity-30 group-hover:opacity-50 transition duration-1000" />
              <Card className="relative shadow-2xl border-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-3xl overflow-hidden">
                <CardHeader className="text-center pb-6 pt-8">
                  <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                    Choose Your Sign In Method
                  </CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-300">
                    Sign in with Google for instant access, or use email as backup
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 px-8 pb-8">
                  {/* Google One Tap - Auto popup */}
                  <GoogleOneTap clientId={googleClientId} enabled={!!googleClientId} />

                  {/* Neon Auth Fallback Button */}
                  <NeonFallbackButton />

                  {/* Terms */}
                  <div className="text-center text-xs text-gray-500 dark:text-gray-400 pt-4 border-t border-gray-200 dark:border-gray-700">
                    By signing in, you agree to our{" "}
                    <Link
                      to="/legal/terms"
                      className="text-blue-600 hover:text-blue-500 dark:text-blue-400 font-medium hover:underline"
                    >
                      Terms of Service
                    </Link>{" "}
                    and{" "}
                    <Link
                      to="/legal/privacy"
                      className="text-blue-600 hover:text-blue-500 dark:text-blue-400 font-medium hover:underline"
                    >
                      Privacy Policy
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sign Up Link */}
            <div className="text-center mt-8 animate-fade-in-up delay-400">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Don't have an account?{" "}
                <Link
                  to="/auth/register"
                  className="text-blue-600 hover:text-blue-500 dark:text-blue-400 font-medium hover:underline"
                >
                  Sign up here
                </Link>
              </p>
            </div>

            {/* Help Text */}
            <div className="text-center mt-4 animate-fade-in-up delay-500">
              <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center justify-center gap-2">
                <svg
                  className="w-4 h-4 text-green-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                Secure authentication powered by Google & Neon
              </p>
            </div>
          </div>
        </div>
      </section>
    </UnifiedLayout>
  );
}
