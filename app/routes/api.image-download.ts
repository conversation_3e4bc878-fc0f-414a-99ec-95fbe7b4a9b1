// app/routes/api.image-download.tsx

import type { LoaderFunctionArgs } from "@remix-run/node";
import { and, eq, isNull } from "drizzle-orm";
import { requireUser } from "~/lib/auth/middleware.server"; // Assuming this utility for auth
import { createDbFromEnv } from "~/lib/db";
import { generatedImages } from "~/lib/db/schema";
import { createImageStorageService } from "~/lib/storage/image-storage.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  // Create database connection
  const db = context.db || createDbFromEnv();
  if (!db) {
    return new Response("Database not available", { status: 503 });
  }

  // Initialize ImageStorageService
  const imageService = createImageStorageService();

  try {
    const user = await requireUser(request); // Authenticate the user

    const url = new URL(request.url);
    const key = url.searchParams.get("key");

    if (!key) {
      return new Response("Missing image key", { status: 400 });
    }

    // Fetch image metadata from DB
    const imageRecord = await db.query.generatedImages.findFirst({
      where: and(
        eq(generatedImages.r2Key, key),
        isNull(generatedImages.deletedAt) // Ensure image is not soft-deleted
      ),
      columns: {
        userId: true,
        imageFormat: true,
        // other fields could be selected if needed for more complex access logic
      },
    });

    if (!imageRecord) {
      return new Response("Image not found or has been deleted", { status: 404 });
    }

    // Access Control Logic:
    // 1. Check if the authenticated user owns the image.
    // 2. Allow access if imageRecord.userId is null (e.g. system-wide images, if applicable).
    //    This part can be adjusted based on application's needs for public/unowned images.
    //    For now, if userId is on the record, it must match. If no userId, it's considered accessible by any authenticated user.
    if (imageRecord.userId && imageRecord.userId !== user.id) {
      return new Response("Forbidden: You do not have access to this image", { status: 403 });
    }

    // Get the image URL from Vercel Blob
    const imageUrl = await imageService.getImageUrl(key);

    if (!imageUrl) {
      console.error(
        `Image record found in DB for key ${key}, but not in Vercel Blob. Potential inconsistency.`
      );
      return new Response("Image data not found in storage", { status: 404 });
    }

    // Redirect to the Vercel Blob URL
    return Response.redirect(imageUrl, 302);
  } catch (error) {
    // Specific handling for auth errors thrown by requireUser
    if (error instanceof Response && (error.status === 401 || error.status === 302)) {
      return error; // Re-throw the auth error/redirect response
    }
    console.error("Image download error:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    return new Response(`Failed to download image: ${errorMessage}`, { status: 500 });
  }
}
