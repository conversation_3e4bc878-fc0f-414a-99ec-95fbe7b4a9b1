import type { MetaFunction } from "@remix-run/node";
import { <PERSON> } from "@remix-run/react";
import {
  Activity,
  ArrowRight,
  Code,
  Database,
  FileText,
  Globe,
  HardDrive,
  Palette,
  TestTube,
  Zap,
} from "lucide-react";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export const meta: MetaFunction = () => {
  return [
    { title: "Developer Center - AI SaaS Starter" },
    {
      name: "description",
      content: "Development tools and testing page navigation center for developers",
    },
  ];
};

const testPages = [
  {
    title: "🧪 Development Test Suite",
    description: "Comprehensive testing page with theme, language, database, and storage tests",
    href: "/dev.test-suite",
    icon: TestTube,
    category: "Testing",
    status: "Recommended",
    features: [
      "Theme Toggle",
      "Language Switch",
      "Database Test",
      "R2 Storage",
      "Performance Monitor",
    ],
  },
  {
    title: "🎨 Component Showcase",
    description: "Display project UI component library",
    href: "/dev.components",
    icon: Code,
    category: "UI Components",
    status: "Stable",
    features: ["shadcn/ui", "Custom Components", "Interactive Demo"],
  },
  {
    title: "⚡ Performance Monitor",
    description: "Real-time page performance metrics and Core Web Vitals",
    href: "/dev.performance",
    icon: Activity,
    category: "Performance",
    status: "Stable",
    features: ["Core Web Vitals", "Load Time", "Resource Monitor"],
  },
  {
    title: "🗄️ Database Testing",
    description: "Test Neon database connection and basic operations",
    href: "/dev.database",
    icon: Database,
    category: "Backend",
    status: "Stable",
    features: ["Connection Test", "CRUD Operations", "Error Handling"],
  },

  {
    title: "🖼️ Image Gallery",
    description: "Showcase of project visual assets and icons",
    href: "/dev/image-gallery",
    icon: Palette,
    category: "Assets",
    status: "Stable",
    features: ["Hero Backgrounds", "Feature Icons", "User Avatars"],
  },
  {
    title: "🔄 State Management Demo",
    description: "Zustand state management demonstration",
    href: "/zustand",
    icon: Zap,
    category: "State",
    status: "Stable",
    features: ["Global State", "Persistence", "Complex Interactions"],
  },
  {
    title: "🤖 AI Tools Suite",
    description: "Complete AI tools for text generation, streaming, and image creation",
    href: "/dev.ai-tools",
    icon: Zap,
    category: "AI Tools",
    status: "Stable",
    features: ["Text Generation", "Stream Text", "Image Generation", "Multiple Providers"],
  },
  {
    title: "⚡ Cloudflare AI Demo",
    description: "Edge AI computing with Cloudflare Workers AI",
    href: "/dev.cloudflare-ai",
    icon: Zap,
    category: "Edge AI",
    status: "Stable",
    features: ["Edge Computing", "Text Classification", "Image Analysis", "Embeddings"],
  },
  {
    title: "🗄️ R2 Storage Test",
    description: "Test Cloudflare R2 storage functionality",
    href: "/dev/r2-test",
    icon: Database,
    category: "Storage",
    status: "Stable",
    features: ["File Upload", "File List", "File Delete", "Storage Config"],
  },
];

const utilityPages = [
  {
    title: "📄 Privacy Policy",
    href: "/legal.privacy",
    icon: FileText,
    description: "View privacy policy page",
  },
  {
    title: "📋 Terms of Service",
    href: "/legal.terms",
    icon: FileText,
    description: "View terms of service page",
  },
  {
    title: "🤝 Website Agreement",
    href: "/legal.agreement",
    icon: FileText,
    description: "View website usage agreement",
  },
];

export default function DevCenter() {
  return (
    <UnifiedLayout
      hero={{
        title: "Developer Center",
        description:
          "Comprehensive development tools and testing page navigation center. Access all development utilities, testing interfaces, and debugging tools in one place.",
      }}
    >
      <section className="py-16">
        <div className="max-w-6xl mx-auto px-4 space-y-8">
          {/* Tech Stack Badges */}
          <div className="flex justify-center gap-2 flex-wrap">
            <Badge variant="outline">Remix</Badge>
            <Badge variant="outline">Cloudflare</Badge>
            <Badge variant="outline">TypeScript</Badge>
            <Badge variant="outline">Zustand</Badge>
          </div>

          {/* Quick Access */}
          <Card className="border-primary/20 bg-primary/5">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-primary" />
                Quick Access
              </CardTitle>
              <CardDescription>Most commonly used development and testing pages</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Button asChild variant="outline" className="h-auto p-4 justify-start">
                  <Link to="/dev.test-suite" className="flex items-center gap-3">
                    <TestTube className="h-5 w-5" />
                    <div className="text-left">
                      <div className="font-medium">Test Suite</div>
                      <div className="text-sm text-muted-foreground">Comprehensive testing</div>
                    </div>
                  </Link>
                </Button>
                <Button asChild variant="outline" className="h-auto p-4 justify-start">
                  <Link to="/dev.performance" className="flex items-center gap-3">
                    <Activity className="h-5 w-5" />
                    <div className="text-left">
                      <div className="font-medium">Performance</div>
                      <div className="text-sm text-muted-foreground">Real-time metrics</div>
                    </div>
                  </Link>
                </Button>
                <Button asChild variant="outline" className="h-auto p-4 justify-start">
                  <Link to="/dev.components" className="flex items-center gap-3">
                    <Code className="h-5 w-5" />
                    <div className="text-left">
                      <div className="font-medium">Components</div>
                      <div className="text-sm text-muted-foreground">UI component library</div>
                    </div>
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Test Pages */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold">🧪 Development Tools</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {testPages.map((page) => (
                <Card key={page.href} className="group hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <page.icon className="h-5 w-5" />
                        <Badge variant="secondary" className="text-xs">
                          {page.category}
                        </Badge>
                      </div>
                      <Badge
                        variant={page.status === "Recommended" ? "default" : "outline"}
                        className="text-xs"
                      >
                        {page.status}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg">{page.title}</CardTitle>
                    <CardDescription>{page.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex flex-wrap gap-1">
                      {page.features.map((feature) => (
                        <Badge key={feature} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                    <Button asChild className="w-full group-hover:bg-primary/90">
                      <Link to={page.href} className="flex items-center gap-2">
                        Visit Page
                        <ArrowRight className="h-4 w-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Utility Pages */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold">📄 Legal Pages</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {utilityPages.map((page) => (
                <Card key={page.href} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <page.icon className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{page.title}</div>
                          <div className="text-sm text-muted-foreground">{page.description}</div>
                        </div>
                      </div>
                      <Button asChild variant="ghost" size="sm">
                        <Link to={page.href}>
                          <ArrowRight className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Development Info */}
          <Card>
            <CardHeader>
              <CardTitle>💡 Development Guide</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium mb-2">Tech Stack</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• Remix (Full-stack framework)</li>
                    <li>• Cloudflare Workers (Deployment platform)</li>
                    <li>• Zustand (State management)</li>

                    <li>• shadcn/ui (UI components)</li>
                    <li>• Tailwind CSS (Styling)</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Testing Recommendations</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• Start with "Test Suite" for comprehensive testing</li>
                    <li>• Check theme switching</li>
                    <li>• Verify database and storage connections</li>
                    <li>• Monitor page performance metrics</li>
                    <li>• Test responsive design</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </UnifiedLayout>
  );
}
