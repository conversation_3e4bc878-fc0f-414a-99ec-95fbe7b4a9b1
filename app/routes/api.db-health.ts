import { json } from "@remix-run/node";
import { createDb } from "~/lib/db/db";

export async function loader() {
  try {
    // Check if DATABASE_URL is available
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      return json(
        {
          status: "error",
          message: "DATABASE_URL not configured",
          timestamp: new Date().toISOString(),
        },
        { status: 500 }
      );
    }

    console.log("Testing database connection...");

    const db = createDb({
      url: databaseUrl,
      connectionTimeoutMillis: 5000, // 5 seconds for health check
      enableLogging: true,
    });

    // Test basic connection with a simple query
    const result = await db.execute("SELECT 1 as test");

    console.log("Database connection successful:", result);

    return json({
      status: "healthy",
      message: "Database connection successful",
      timestamp: new Date().toISOString(),
      testResult: result,
    });
  } catch (error) {
    console.error("Database health check failed:", error);

    return json(
      {
        status: "unhealthy",
        message: error instanceof Error ? error.message : "Unknown database error",
        timestamp: new Date().toISOString(),
        error: {
          name: error instanceof Error ? error.name : "UnknownError",
          message: error instanceof Error ? error.message : "Unknown error occurred",
        },
      },
      { status: 500 }
    );
  }
}
