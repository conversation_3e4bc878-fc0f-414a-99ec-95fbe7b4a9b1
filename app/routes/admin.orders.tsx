import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { OrderDataTable } from "~/components/admin/SearchableDataTable";
import { createDbFromEnv } from "~/lib/db";
import { searchOrders } from "~/models/order";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // Create database connection
    const db = context.db || createDbFromEnv();

    // Parse search parameters
    const url = new URL(request.url);
    const searchParams = {
      search: url.searchParams.get("search") || undefined,
      page: parseInt(url.searchParams.get("page") || "1"),
      limit: parseInt(url.searchParams.get("limit") || "20"),
      sortBy: url.searchParams.get("sortBy") || "createdAt",
      sortOrder: (url.searchParams.get("sortOrder") as "asc" | "desc") || "desc",
      status: url.searchParams.get("status")?.split(",") || undefined,
      minAmount: url.searchParams.get("minAmount")
        ? parseFloat(url.searchParams.get("minAmount")!)
        : undefined,
      maxAmount: url.searchParams.get("maxAmount")
        ? parseFloat(url.searchParams.get("maxAmount")!)
        : undefined,
      dateFrom: url.searchParams.get("dateFrom")
        ? new Date(url.searchParams.get("dateFrom")!)
        : undefined,
      dateTo: url.searchParams.get("dateTo")
        ? new Date(url.searchParams.get("dateTo")!)
        : undefined,
    };

    // Search orders
    const result = await searchOrders(db, searchParams);

    return json({
      success: true,
      data: result.data,
      pagination: result.pagination,
      searchParams,
    });
  } catch (error) {
    console.error("Error loading orders:", error);
    return json(
      {
        success: false,
        error: "Failed to load orders",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

export default function AdminOrders() {
  const loaderData = useLoaderData<typeof loader>();

  // Handle both success and error cases
  if (!loaderData.success || !loaderData.data) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Failed to load orders</h1>
          <p className="text-gray-600">Please try refreshing the page.</p>
        </div>
      </div>
    );
  }

  const { data, pagination } = loaderData;

  const handleOrderAction = (action: string, order: any) => {
    console.log(`${action} order:`, order);
    // TODO: Implement order actions
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <OrderDataTable
          data={data}
          pagination={pagination}
          onOrderAction={handleOrderAction}
          title="Order Management"
          subtitle="Manage and track all orders in your application"
          headerActions={
            <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Export Orders
            </button>
          }
        />
      </div>
    </div>
  );
}
