import type { MetaFunction } from "@remix-run/node";
import UnifiedLayout from "~/components/layout/unified-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { getPageConfig } from "~/config/seo";

// Removed loader function

export const meta: MetaFunction = () => {
  const pageConfig = getPageConfig("agreement");
  return [
    { title: pageConfig.title },
    { name: "description", content: pageConfig.description },
    { name: "keywords", content: pageConfig.keywords },
    ...(pageConfig.noIndex ? [{ name: "robots", content: "noindex" }] : []),
  ];
};

export default function WebsiteAgreement() {
  const lastUpdated = new Date().toLocaleDateString(undefined, {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <UnifiedLayout
      hero={{
        title: "Website User Agreement",
        description: `Last Updated: ${lastUpdated}. This agreement outlines the terms and conditions for using our website.`,
      }}
    >
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Agreement to Terms</CardTitle> {/* Static text */}
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <p>
                By using this website, you signify your acceptance of this User Agreement. If you do
                not agree to these terms, please do not use our website.
              </p>{" "}
              {/* Static text */}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Permitted Use</CardTitle> {/* Static text */}
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <p>
                You are granted a non-exclusive, non-transferable, revocable license to access and
                use the website strictly in accordance with this agreement.
              </p>{" "}
              {/* Static text */}
              <ul>
                <li>You agree not to use the website for any unlawful purpose.</li>{" "}
                {/* Static text */}
                <li>
                  You agree not to disrupt or interfere with the security of, or otherwise abuse,
                  the website.
                </li>{" "}
                {/* Static text */}
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Intellectual Property Rights</CardTitle> {/* Static text */}
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <p>
                All content on this website, including text, graphics, logos, and images, is the
                property of our company or its content suppliers and protected by copyright and
                other intellectual property laws.
              </p>{" "}
              {/* Static text */}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>User Content</CardTitle> {/* Static text */}
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <p>
                If you post, upload, or make available any content on the website, you grant us a
                non-exclusive, royalty-free, perpetual, and worldwide license to use, reproduce,
                modify, and display such content.
              </p>{" "}
              {/* Static text */}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Disclaimer of Warranties</CardTitle> {/* Static text */}
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <p>
                The website is provided on an "as is" and "as available" basis. We make no
                warranties, expressed or implied, regarding the operation or availability of the
                website.
              </p>{" "}
              {/* Static text */}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Limitation of Liability</CardTitle> {/* Static text */}
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <p>
                Our company will not be liable for any damages of any kind arising from the use of
                this website, including, but not limited to direct, indirect, incidental, punitive,
                and consequential damages.
              </p>{" "}
              {/* Static text */}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Indemnification</CardTitle> {/* Static text */}
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <p>
                You agree to indemnify, defend, and hold harmless our company, its officers,
                directors, employees, agents, and third parties, for any losses, costs, liabilities,
                and expenses relating to or arising out of your use of or inability to use the
                website.
              </p>{" "}
              {/* Static text */}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Changes to Agreement</CardTitle> {/* Static text */}
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <p>
                We reserve the right to change this User Agreement at any time. Your continued use
                of the website following the posting of changes will mean that you accept and agree
                to the changes.
              </p>{" "}
              {/* Static text */}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle> {/* Static text */}
            </CardHeader>
            <CardContent className="prose prose-slate dark:prose-invert max-w-none">
              <p>
                If you have any questions about this User Agreement, please contact us at [Your
                Contact Email/Link].
              </p>{" "}
              {/* Static text */}
            </CardContent>
          </Card>
        </div>
      </section>
    </UnifiedLayout>
  );
}
