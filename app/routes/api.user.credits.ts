import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { requireUser } from "~/lib/auth/middleware.server";
import { createDbFromEnv } from "~/lib/db";
import { getUserCreditBalance, getUserCreditHistory } from "~/models/user";

// Type definitions for request body
interface CreditHistoryBody {
  page?: number;
  limit?: number;
  dateFrom?: string;
  dateTo?: string;
  transactionType?: string;
}

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    // Create database connection
    const db = context.db || createDbFromEnv();
    if (!db) {
      return json({ success: false, error: "Database connection failed" }, { status: 500 });
    }

    // Get current user
    const user = await requireUser(request);
    const userUuid = user.uuid;

    // Parse parameters
    const url = new URL(request.url);
    const includeBalance = url.searchParams.get("balance") === "true";
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "20");

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return json({ success: false, error: "Invalid pagination parameters" }, { status: 400 });
    }

    // Get user credit history and optionally current balance
    const [historyResult, currentBalance] = await Promise.all([
      getUserCreditHistory(userUuid, db, { page, limit }),
      includeBalance ? getUserCreditBalance(userUuid, db) : null,
    ]);

    const response: any = {
      success: true,
      data: historyResult.data,
      pagination: historyResult.pagination,
    };

    if (includeBalance !== null) {
      response.currentBalance = currentBalance;
      response.timestamp = new Date().toISOString();
    }

    return json(response);
  } catch (error) {
    console.error("Get user credit history failed:", error);
    return json(
      {
        success: false,
        error: "Failed to get credit history",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// POST method for more complex queries (e.g., date range filtering)
export async function action({ request, context }: LoaderFunctionArgs) {
  if (request.method !== "POST") {
    return json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const db = context.db || createDbFromEnv();
    if (!db) {
      return json({ success: false, error: "Database connection failed" }, { status: 500 });
    }

    const user = await requireUser(request);
    const userUuid = user.uuid;

    // Parse request body
    const body = (await request.json()) as CreditHistoryBody;
    const { page = 1, limit = 20, dateFrom, dateTo, transactionType } = body;

    // Validate parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return json({ success: false, error: "Invalid pagination parameters" }, { status: 400 });
    }

    // Get filtered credit history
    const result = await getUserCreditHistory(userUuid, db, {
      page,
      limit,
      // Note: The current getUserCreditHistory doesn't support filtering
      // This would need to be enhanced in the future
    });

    // Filter results if date range is provided (client-side filtering for now)
    let filteredData = result.data;
    if (dateFrom || dateTo || transactionType) {
      filteredData = result.data.filter((transaction: any) => {
        let matches = true;

        if (dateFrom) {
          matches = matches && new Date(transaction.createdAt) >= new Date(dateFrom);
        }

        if (dateTo) {
          matches = matches && new Date(transaction.createdAt) <= new Date(dateTo);
        }

        if (transactionType) {
          matches = matches && transaction.transType === transactionType;
        }

        return matches;
      });
    }

    return json({
      success: true,
      data: filteredData,
      pagination: {
        ...result.pagination,
        total: filteredData.length, // Update total for filtered results
      },
      filters: {
        dateFrom,
        dateTo,
        transactionType,
      },
    });
  } catch (error) {
    console.error("Get filtered credit history failed:", error);
    return json(
      {
        success: false,
        error: "Failed to get filtered credit history",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
