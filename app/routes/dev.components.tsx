import type { MetaFunction } from "@remix-run/node";
import { Download, <PERSON>, Settings, Star } from "lucide-react";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { getPageConfig } from "~/config/seo"; // Updated import

// Removed loader function

export const meta: MetaFunction = () => {
  const pageConfig = getPageConfig("components");
  return [
    { title: pageConfig.title },
    { name: "description", content: pageConfig.description },
    { name: "keywords", content: pageConfig.keywords },
  ];
};

export default function ComponentsDemo() {
  return (
    <UnifiedLayout
      headerProps={{}}
      hero={{
        badge: {
          text: "🎨 UI Components",
        },
        title: "Component Library", // Static text
        subtitle: "Design System Showcase",
        description:
          "Explore our comprehensive collection of UI components built with modern design principles and accessibility in mind.", // Static text
        backgroundPattern: "gradient",
      }}
    >
      <section className="py-16">
        <div className="max-w-6xl mx-auto px-4 space-y-8">
          {/* Buttons Section */}
          <Card>
            <CardHeader>
              <CardTitle>Buttons</CardTitle> {/* Static text */}
              <CardDescription>Various button styles and options</CardDescription>{" "}
              {/* Static text */}
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-4">
                <Button>Default</Button> {/* Static text */}
                <Button variant="secondary">Secondary</Button> {/* Static text */}
                <Button variant="outline">Outline</Button> {/* Static text */}
                <Button variant="ghost">Ghost</Button> {/* Static text */}
                <Button variant="destructive">Destructive</Button> {/* Static text */}
                <Button variant="link">Link</Button> {/* Static text */}
              </div>

              <div className="flex flex-wrap gap-4 items-center">
                <Button size="sm">Small</Button> {/* Static text */}
                <Button size="default">Default Size</Button> {/* Static text */}
                <Button size="lg">Large</Button> {/* Static text */}
                <Button size="icon">
                  <Heart className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex flex-wrap gap-4">
                <Button>
                  <Download className="mr-2 h-4 w-4" />
                  Download {/* Static text */}
                </Button>
                <Button variant="outline">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings {/* Static text */}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Cards Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-500" />
                  Featured
                </CardTitle>
                <CardDescription>This is a featured card with an icon</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor
                  incididunt ut labore.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Simple Card</CardTitle>
                <CardDescription>A basic card example</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Badge>New</Badge>
                  <Badge variant="secondary">Popular</Badge>
                  <Badge variant="outline">Limited</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Interactive Card</CardTitle>
                <CardDescription>Card with form elements</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input placeholder="Enter your email" />
                <Button className="w-full">Subscribe</Button>
              </CardContent>
            </Card>
          </div>

          {/* Badges Section */}
          <Card>
            <CardHeader>
              <CardTitle>Badges</CardTitle>
              <CardDescription>Different badge variants</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                <Badge>Default</Badge>
                <Badge variant="secondary">Secondary</Badge>
                <Badge variant="outline">Outline</Badge>
                <Badge variant="destructive">Destructive</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Input Section */}
          <Card>
            <CardHeader>
              <CardTitle>Input Fields</CardTitle>
              <CardDescription>Various input examples</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input placeholder="Default input" />
                <Input placeholder="Email" type="email" />
                <Input placeholder="Password" type="password" />
                <Input placeholder="Disabled" disabled />
              </div>
            </CardContent>
          </Card>

          {/* Dark Mode Toggle Info */}
          <Card>
            <CardHeader>
              <CardTitle>Dark Mode Support</CardTitle>
              <CardDescription>All components support dark mode automatically</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                The components automatically adapt to your system's dark mode preference. You can
                also implement a manual dark mode toggle using the "dark" class.
              </p>
            </CardContent>
          </Card>

          {/* Dark Mode Toggle Info */}
          <Card>
            <CardHeader>
              <CardTitle>Development Notes</CardTitle>
              <CardDescription>Additional information about the component system</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                All components are built with TypeScript, support dark mode automatically, and
                follow accessibility best practices. The design system is optimized for American
                users with modern design patterns.
              </p>
            </CardContent>
          </Card>
        </div>
      </section>
    </UnifiedLayout>
  );
}
