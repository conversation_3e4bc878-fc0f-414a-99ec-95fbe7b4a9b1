import { execSync } from "child_process";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const certsDir = path.join(__dirname, "..", ".certs");

function commandExists(command: string): boolean {
  try {
    const checkCommand =
      process.platform === "win32" ? `where ${command}` : `command -v ${command}`;
    execSync(checkCommand, { stdio: "ignore" });
    return true;
  } catch (error) {
    return false;
  }
}

function setup() {
  console.log("Checking for mkcert...");
  if (!commandExists("mkcert")) {
    if (process.platform === "win32") {
      console.log("mkcert not found. Attempting to install with winget...");
      try {
        execSync(
          "winget install --id FiloSottile.mkcert --source winget --accept-source-agreements --accept-package-agreements",
          { stdio: "inherit" }
        );
        if (!commandExists("mkcert")) {
          console.error(
            "❌ mkcert installation via winget seemed to succeed, but it is still not in your PATH."
          );
          console.log(
            "Please open a new terminal and try again, or install it manually from https://github.com/FiloSottile/mkcert"
          );
          process.exit(1);
        }
        console.log("✅ mkcert installed successfully via winget.");
      } catch (error) {
        console.error("❌ Failed to install mkcert using winget.");
        console.log(
          "Please install it manually by following the instructions at https://github.com/FiloSottile/mkcert"
        );
        console.log("You can also use: choco install mkcert or scoop install mkcert");
        process.exit(1);
      }
    } else {
      console.error("❌ mkcert is not installed or not in your PATH.");
      console.log(
        "Please install it by following the instructions at https://github.com/FiloSottile/mkcert"
      );
      process.exit(1);
    }
  }
  console.log("✅ mkcert is available.");

  if (!fs.existsSync(certsDir)) {
    console.log(`📁 Creating certificate directory: ${certsDir}`);
    fs.mkdirSync(certsDir, { recursive: true });
  }

  try {
    console.log("🔐 Installing CA root certificate (you might be asked for your password)...");
    execSync("mkcert -install");
    console.log("✅ CA root certificate installed successfully.");
  } catch (error) {
    console.error(`❌ Failed to install CA root certificate: ${error.message}`);
    process.exit(1);
  }

  const keyFile = path.join(certsDir, "key.pem");
  const certFile = path.join(certsDir, "cert.pem");

  try {
    console.log("🔑 Generating local certificate...");
    execSync(`mkcert -key-file "${keyFile}" -cert-file "${certFile}" localhost 127.0.0.1 ::1`);
    console.log(`✅ Certificate generated successfully at ${certsDir}`);
  } catch (error) {
    console.error(`❌ Failed to generate certificate: ${error.message}`);
    process.exit(1);
  }
}

setup();
