#!/usr/bin/env tsx

/**
 * 设置 HTTPS 开发环境脚本
 * 用于解决 Google 认证等需要安全上下文的功能
 */

import { execSync } from "node:child_process";
import { existsSync, mkdirSync, readFileSync, writeFileSync } from "node:fs";
import { join } from "node:path";

const CERT_DIR = join(process.cwd(), ".certs");
const CERT_FILE = join(CERT_DIR, "localhost.pem");
const KEY_FILE = join(CERT_DIR, "localhost-key.pem");

console.log("🔧 设置 HTTPS 开发环境...\n");

// 检查是否已安装 mkcert
function checkMkcert(): boolean {
  try {
    execSync("mkcert -version", { stdio: "ignore" });
    return true;
  } catch {
    return false;
  }
}

// 安装 mkcert
function installMkcert(): void {
  console.log("📦 安装 mkcert...");

  const platform = process.platform;

  try {
    if (platform === "darwin") {
      // macOS
      execSync("brew install mkcert", { stdio: "inherit" });
    } else if (platform === "linux") {
      // Linux - 尝试不同的包管理器
      try {
        execSync("sudo apt-get update && sudo apt-get install -y mkcert", { stdio: "inherit" });
      } catch {
        try {
          execSync("sudo yum install -y mkcert", { stdio: "inherit" });
        } catch {
          console.log("❌ 无法自动安装 mkcert，请手动安装：");
          console.log("   Ubuntu/Debian: sudo apt-get install mkcert");
          console.log("   CentOS/RHEL: sudo yum install mkcert");
          console.log("   或访问: https://github.com/FiloSottile/mkcert#installation");
          process.exit(1);
        }
      }
    } else if (platform === "win32") {
      // Windows
      try {
        execSync("choco install mkcert", { stdio: "inherit" });
      } catch {
        try {
          execSync("scoop install mkcert", { stdio: "inherit" });
        } catch {
          console.log("❌ 无法自动安装 mkcert，请手动安装：");
          console.log("   使用 Chocolatey: choco install mkcert");
          console.log("   使用 Scoop: scoop install mkcert");
          console.log("   或访问: https://github.com/FiloSottile/mkcert#installation");
          process.exit(1);
        }
      }
    } else {
      console.log("❌ 不支持的操作系统，请手动安装 mkcert");
      console.log("   访问: https://github.com/FiloSottile/mkcert#installation");
      process.exit(1);
    }

    console.log("✅ mkcert 安装成功");
  } catch (error) {
    console.error("❌ 安装 mkcert 失败:", (error as Error).message);
    process.exit(1);
  }
}

// 创建证书目录
function createCertDir(): void {
  if (!existsSync(CERT_DIR)) {
    mkdirSync(CERT_DIR, { recursive: true });
    console.log("📁 创建证书目录:", CERT_DIR);
  }
}

// 安装 CA 根证书
function installCA(): void {
  console.log("🔐 安装 CA 根证书...");
  try {
    execSync("mkcert -install", { stdio: "inherit" });
    console.log("✅ CA 根证书安装成功");
  } catch (error) {
    console.error("❌ 安装 CA 根证书失败:", (error as Error).message);
    process.exit(1);
  }
}

// 生成本地证书
function generateCerts(): void {
  console.log("📜 生成本地 HTTPS 证书...");

  const domains = ["localhost", "127.0.0.1", "::1", "0.0.0.0"];

  try {
    const cmd = `mkcert -key-file ${KEY_FILE} -cert-file ${CERT_FILE} ${domains.join(" ")}`;
    execSync(cmd, { stdio: "inherit", cwd: CERT_DIR });
    console.log("✅ 证书生成成功");
    console.log(`   证书文件: ${CERT_FILE}`);
    console.log(`   私钥文件: ${KEY_FILE}`);
  } catch (error) {
    console.error("❌ 生成证书失败:", (error as Error).message);
    process.exit(1);
  }
}

// 更新 Vite 配置
function updateViteConfig(): void {
  console.log("⚙️  更新 Vite 配置...");

  const viteConfigPath = join(process.cwd(), "vite.config.ts");

  if (!existsSync(viteConfigPath)) {
    console.log("❌ 找不到 vite.config.ts 文件");
    return;
  }

  let config = readFileSync(viteConfigPath, "utf-8");

  // 检查是否已经配置了 HTTPS
  if (config.includes("readFileSync") && config.includes(".certs")) {
    console.log("✅ Vite 配置已包含 HTTPS 设置");
    return;
  }

  // 添加必要的导入
  if (!config.includes("import { readFileSync, existsSync }")) {
    config = config.replace(
      'import path from "path";',
      'import path from "path";\nimport { readFileSync, existsSync } from "fs";'
    );
  }

  // 更新服务器配置
  const serverConfigRegex = /server:\s*\{[^}]*\}/s;
  const newServerConfig = `server: (() => {
    const baseConfig = {
      port: 5174,
      strictPort: true,
      host: true,
    };

    if (process.env.HTTPS === 'true') {
      const certPath = join(__dirname, '.certs/localhost.pem');
      const keyPath = join(__dirname, '.certs/localhost-key.pem');
      
      if (existsSync(certPath) && existsSync(keyPath)) {
        return {
          ...baseConfig,
          https: {
            key: readFileSync(keyPath),
            cert: readFileSync(certPath),
          }
        };
      } else {
        console.warn('⚠️  HTTPS 证书文件不存在');
        return { ...baseConfig, https: true };
      }
    }

    return baseConfig;
  })()`;

  config = config.replace(serverConfigRegex, newServerConfig);

  writeFileSync(viteConfigPath, config);
  console.log("✅ Vite 配置已更新");
}

// 更新 package.json 脚本
function updatePackageScripts(): void {
  console.log("📝 更新 package.json 脚本...");

  const packageJsonPath = join(process.cwd(), "package.json");
  const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf-8"));

  // 添加 HTTPS 开发脚本
  packageJson.scripts = {
    ...packageJson.scripts,
    "dev:https": "HTTPS=true remix vite:dev",
    "dev:http": "remix vite:dev",
  };

  writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log("✅ package.json 脚本已更新");
}

// 创建 .gitignore 条目
function updateGitignore(): void {
  const gitignorePath = join(process.cwd(), ".gitignore");

  if (existsSync(gitignorePath)) {
    let gitignore = readFileSync(gitignorePath, "utf-8");

    if (!gitignore.includes(".certs/")) {
      gitignore += "\n# HTTPS certificates\n.certs/\n";
      writeFileSync(gitignorePath, gitignore);
      console.log("✅ 已添加 .certs/ 到 .gitignore");
    }
  }
}

// 主函数
function main(): void {
  try {
    // 检查并安装 mkcert
    if (!checkMkcert()) {
      installMkcert();
    } else {
      console.log("✅ mkcert 已安装");
    }

    // 创建证书目录
    createCertDir();

    // 安装 CA 根证书
    installCA();

    // 生成本地证书
    generateCerts();

    // 更新配置文件
    updateViteConfig();
    updatePackageScripts();
    updateGitignore();

    console.log("\n🎉 HTTPS 开发环境设置完成！");
    console.log("\n📋 下一步：");
    console.log("1. 运行 `pnpm run dev:https` 启动 HTTPS 开发服务器");
    console.log("2. 访问 https://localhost:5174 (接受证书警告)");
    console.log("3. 在 Google Console 中添加 https://localhost:5174 到授权域名");
    console.log("4. 测试 Google 认证：https://localhost:5174/test/google-auth");
  } catch (error) {
    console.error("❌ 设置失败:", (error as Error).message);
    process.exit(1);
  }
}

// 运行主函数
main();
