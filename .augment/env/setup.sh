#!/bin/bash
set -e

echo "🚀 Setting up Remix + Vercel + Neon development environment..."

# Update system packages
sudo apt-get update -y

# Install Node.js 20 (LTS) via NodeSource repository
echo "📦 Installing Node.js 20..."
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js installation
node_version=$(node --version)
npm_version=$(npm --version)
echo "✅ Node.js version: $node_version"
echo "✅ npm version: $npm_version"

# Install pnpm using corepack (recommended method)
echo "📦 Installing pnpm package manager via corepack..."
sudo corepack enable
corepack prepare pnpm@latest --activate

# Verify pnpm installation
pnpm_version=$(pnpm --version)
echo "✅ pnpm version: $pnpm_version"

# Add pnpm to PATH in user profile
echo 'export PATH="$PATH:$(pnpm root -g)/bin"' >> $HOME/.profile

# Install project dependencies
echo "📦 Installing project dependencies..."
cd /mnt/persist/workspace
pnpm install --frozen-lockfile

# Verify TypeScript installation
echo "🔍 Checking TypeScript..."
if pnpm tsc --version &> /dev/null; then
    tsc_version=$(pnpm tsc --version)
    echo "✅ TypeScript version: $tsc_version"
else
    echo "✅ TypeScript: Available via project dependencies"
fi

# Check if Vercel CLI is available
echo "🔍 Checking Vercel CLI..."
if pnpm vercel --version &> /dev/null; then
    vercel_version=$(pnpm vercel --version)
    echo "✅ Vercel CLI version: $vercel_version"
else
    echo "✅ Vercel CLI: Available via project dependencies"
fi

# Verify Vitest installation
echo "🔍 Checking Vitest..."
if pnpm vitest --version &> /dev/null; then
    vitest_version=$(pnpm vitest --version)
    echo "✅ Vitest version: $vitest_version"
else
    echo "✅ Vitest: Available via project dependencies"
fi

# Check Biome (linter/formatter)
echo "🔍 Checking Biome..."
if pnpm biome --version &> /dev/null; then
    biome_version=$(pnpm biome --version)
    echo "✅ Biome version: $biome_version"
else
    echo "✅ Biome: Available via project dependencies"
fi

# Create a simple .env file for testing if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating basic .env file for testing..."
    cat > .env << EOF
NODE_ENV=development
DATABASE_URL=postgresql://user:password@localhost:5432/remix_vercel_neon_starter
RESEND_API_KEY=your_resend_api_key_here
GA_TRACKING_ID=your_google_analytics_id_here
EOF
fi

# Check for database connection (optional)
echo "🔍 Checking database setup..."
if [ -f "drizzle.config.ts" ]; then
    echo "✅ Drizzle ORM configuration found"
    if pnpm drizzle-kit --version &> /dev/null; then
        drizzle_version=$(pnpm drizzle-kit --version)
        echo "✅ Drizzle Kit version: $drizzle_version"
    else
        echo "✅ Drizzle Kit: Available via project dependencies"
    fi
fi

# Verify that basic tools work
echo "🔍 Verifying development tools..."

# Check if we can run basic commands
echo "  - Testing pnpm commands..."
pnpm --version > /dev/null && echo "    ✅ pnpm is working"

echo "  - Testing Node.js..."
node --version > /dev/null && echo "    ✅ Node.js is working"

echo "  - Testing npm..."
npm --version > /dev/null && echo "    ✅ npm is working"

echo "  - Testing project dependencies..."
pnpm list --depth=0 > /dev/null 2>&1 && echo "    ✅ Project dependencies are installed"

echo "🎉 Development environment setup complete!"
echo ""
echo "📝 Available commands:"
echo "  - pnpm dev: Start Remix development server"
echo "  - pnpm build: Build for production"
echo "  - pnpm start: Start production server locally"
echo "  - pnpm deploy: Deploy to Vercel"
echo "  - pnpm test: Run tests with Vitest"
echo "  - pnpm lint: Run Biome linting"
echo "  - pnpm format: Format code with Biome"
echo "  - pnpm typecheck: Run TypeScript checks"
echo "  - pnpm db:generate: Generate database migrations"
echo "  - pnpm db:migrate: Run database migrations"
echo "  - pnpm db:studio: Open Drizzle Studio"
echo ""
echo "🎉 Remix + Vercel + Neon development environment is ready!"
echo "   📊 Database: Neon PostgreSQL with Drizzle ORM"
echo "   🚀 Deployment: Vercel"
echo "   🎨 UI: Tailwind CSS + Radix UI + shadcn/ui"